import { Component, Inject } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Domain, DOMAIN_TYPES, DomainsItemDialogData, DomainType } from '../../../common/models/domain.model';

@Component({
  selector: 'domains-item-dialog',
  templateUrl: 'domains-item-dialog.component.html',
})
export class DomainsItemDialogComponent {
  readonly domainType: DomainType;
  readonly title: string;
  readonly domain?: Domain;
  readonly gameServers: string[] = [];
  readonly form: FormGroup;

  constructor(private dialogRef: MatDialogRef<DomainsItemDialogComponent>,
              private fb: FormBuilder,
              @Inject(MAT_DIALOG_DATA) {type, domain, gameServers}: DomainsItemDialogData) {
    this.domainType = type;
    this.gameServers = gameServers ? gameServers : [];

    let typeName = 'Dynamic';
    if (type === DOMAIN_TYPES.static) {
      typeName = 'Static';
    }
    this.title = `DOMAINS.${(domain && domain.id ? 'edit' : 'add')}${typeName}Domain`;

    this.form = this.fb.group({
      domain: [domain && domain.domain ? domain.domain : '', Validators.required],
    });

    if (type === DOMAIN_TYPES.dynamic) {
      const control = this.fb.control(domain && domain.environment ? domain.environment : '');
      control.setValidators(Validators.required);
      this.form.addControl('environment', control);
      this.form.updateValueAndValidity();
    } else {
      if (this.environmentControl) {
        this.form.removeControl('environment');
        this.form.updateValueAndValidity();
      }
    }
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  submit() {
    this.form.markAllAsTouched();
    if (this.form.valid) {
      this.dialogRef.close(this.form.value);
    }
  }

  get domainControl(): FormControl {
    return this.form.get('domain') as FormControl;
  }

  get environmentControl(): FormControl {
    return this.form.get('environment') as FormControl;
  }
}
