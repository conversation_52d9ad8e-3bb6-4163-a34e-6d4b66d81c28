import { Component, Input, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { RowAction, SettingsService, SwuiGridComponent, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Domain, DomainPool } from '../../../common/models/domain.model';
import { SCHEMA_LIST } from './schema';
import { DEFAULT_PAGE_SIZE } from '../../../app.constants';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil, tap } from 'rxjs/operators';
import { ConfirmationDialogComponent } from '../confirmation-dialog/confirmation-dialog.component';
import { DomainsPoolService } from './domains-pool.service';
import { DomainsPoolDialogComponent, DomainsPoolDialogData } from './dialog/domains-pool-dialog.component';

@Component({
  selector: 'domains-pool',
  templateUrl: './domains-pool.component.html',
})
export class DomainsPoolComponent {
  @Input() staticDomains: Domain[] = [];

  schema = SCHEMA_LIST;
  pageSize = DEFAULT_PAGE_SIZE;

  rowActions: RowAction[] = [];
  data: DomainPool[];

  @ViewChild('grid', {static: true}) public grid: SwuiGridComponent<Domain>;

  private _destroyed$ = new Subject();

  constructor(private service: DomainsPoolService,
              private settingsService: SettingsService,
              private dialog: MatDialog,
              private notifications: SwuiNotificationsService,
              private translate: TranslateService
  ) {
    this.service.isGridChanged$
      .pipe(
        switchMap(() => this.service.getList(undefined, true)),
        takeUntil(this._destroyed$)
      )
      .subscribe((val: Domain[]) => {
        this.data = val;
      });
  }

  ngOnInit() {
    this.settingsService.appSettings$.pipe(takeUntil(this._destroyed$)).subscribe(val => {
      this.pageSize = val.pageSize;
    });
    this.service.getList().pipe(takeUntil(this._destroyed$)).subscribe((val: Domain[]) => {
      this.data = val;
    });
    this.setRowActions();
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  refreshGrid() {
    this.service.isGridChanged$.next();
  }

  private setRowActions() {
    this.rowActions = [
      {
        title: 'DOMAINS.GRID.editPool',
        icon: 'edit',
        fn: (item: DomainPool) => {
          const data: DomainsPoolDialogData = {
            pool: item,
            domains: this.staticDomains
          };
          const dialogRef = this.dialog.open(DomainsPoolDialogComponent, {
            width: '500px',
            data: data,
            disableClose: true
          });

          dialogRef.afterClosed()
            .pipe(
              filter(domainPool => !!domainPool),
              switchMap((domainPool: DomainPool) => this.service.update(item.id, domainPool)),
              switchMap(domainPool => this.translate.get('DOMAINS.notificationPoolModified', {name: domainPool.name})),
              tap((message) => this.notifications.success(message)),
              switchMap(() => this.service.getList(undefined, true)),
              takeUntil(this._destroyed$)
            )
            .subscribe(val => {
              this.data = val;
            });
        },
        canActivateFn: () => true,
      },
      {
        title: 'DOMAINS.GRID.removePool',
        icon: 'delete',
        fn: (item: DomainPool) => {
          const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
            width: '500px',
            data: item,
            disableClose: true
          });

          dialogRef.afterClosed()
            .pipe(
              filter(data => !!data),
              switchMap(() => this.service.delete(item.id)),
              switchMap(() => this.translate.get('DOMAINS.notificationPoolRemoved', {name: item.name})),
              tap((message) => this.notifications.success(message)),
              switchMap(() => this.service.getList(undefined, true)),
              takeUntil(this._destroyed$)
            )
            .subscribe((val: Domain[]) => this.data = val);
        },
        canActivateFn: () => true,
      },
    ];
  }
}
