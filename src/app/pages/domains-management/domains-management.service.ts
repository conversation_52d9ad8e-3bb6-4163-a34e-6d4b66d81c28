import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { Observable, of, Subject, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { API_ENDPOINT, ZERO_TIME } from '../../app.constants';
import { Domain, DOMAIN_TYPES, DomainRow, DomainType } from '../../common/models/domain.model';

@Injectable()
export class DomainsManagementService {
  isGridChanged$ = new Subject<DomainType>();

  private cachedItems: Record<string, Domain[]> = {};

  constructor(
    private readonly http: HttpClient,
    private readonly notifications: SwuiNotificationsService,
  ) { }

  getList(type: DomainType, path?: string, force?: boolean): Observable<Domain[]> {
    if (!force) {
      if (this.cachedItems[DOMAIN_TYPES[type]]) {
        return of(this.cachedItems[DOMAIN_TYPES[type]]);
      }
    }
    return this.http.get<Domain[]>(`${this.getUrl(type, path)}`)
      .pipe(
        map(response => response.map(this.processRecord)),
        tap((data: Domain[]) => {
          this.cachedItems[DOMAIN_TYPES[type]] = data;
        }),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  delete(id: string, type: DomainType): Observable<Object> {
    return this.http.delete(`${this.getUrl(type)}/${id}`)
      .pipe(
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  create(data: Domain, type: DomainType): Observable<Domain> {
    if ('_meta' in data) {
      delete data['_meta'];
    }
    return this.http.post<Domain>(this.getUrl(type), JSON.stringify(data))
      .pipe(
        map(this.processRecord),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  update(id: string, data: Partial<DomainRow>, type: DomainType): Observable<Domain> {
    if ('_meta' in data) {
      delete data['_meta'];
    }
    return this.http.patch<Domain>(`${this.getUrl(type)}/${id}`, JSON.stringify(data))
      .pipe(
        map(this.processRecord),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  private processRecord(record: DomainRow): Domain {
    for (const property of ['createdAt', 'updatedAt']) {
      if (record[property] === ZERO_TIME) {
        record[property] = null;
      }
    }
    return {
      ...record,
      _meta: {
        createdAt: record.createdAt && moment(record.createdAt),
        updatedAt: record.updatedAt && moment(record.updatedAt),
      }
    };
  }

  private getUrl(type: DomainType, path?: string): string {
    return `${API_ENDPOINT}${path && path !== ':' ? '/entities/' + path : ''}/domains/${type}`;
  }
}
