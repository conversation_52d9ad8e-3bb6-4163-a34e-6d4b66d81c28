import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { API_ENDPOINT, ZERO_TIME } from '../../app.constants';
import { Domain, DOMAIN_TYPES, DomainRow, DomainType } from '../../common/models/domain.model';

@Injectable()
export class EntityDomainService {
  private cachedItem: Record<string, Domain> = {};

  constructor(
    private readonly http: HttpClient,
    private readonly notifications: SwuiNotificationsService
  ) { }

  setEntityDomain(type: DomainType, domainId: string, path: string = ':') {
    return this.invoke('PUT', `${this.getUrl(path, type)}${domainId}`, type);
  }

  getEntityDomain(type: DomainType, path: string = ':', force = false) {
    if (!force) {
      if (this.cachedItem[DOMAIN_TYPES[type]]) {
        return of(this.cachedItem[DOMAIN_TYPES[type]]);
      }
    }
    return this.invoke('GET', `${this.getUrl(path, type)}`, type);
  }

  removeEntityDomain(type: DomainType, path: string = ':') {
    return this.invoke('DELETE', `${this.getUrl(path, type)}`, type);
  }

  private invoke(method: string, url: string, type: DomainType) {
    return this.http.request(method, url).pipe(
      map<DomainRow, Domain>((record) => {
        for (const property of ['createdAt', 'updatedAt']) {
          if (record[property] === ZERO_TIME) {
            record[property] = null;
          }
        }
        return {
          ...record,
          _meta: {
            createdAt: record.createdAt && moment(record.createdAt),
            updatedAt: record.updatedAt && moment(record.updatedAt),
          }
        };
      }),
      tap((data: Domain) => {
        this.cachedItem[DOMAIN_TYPES[type]] = data;
      }),
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
    );
  }

  bulkOperation(bulkRequestData: any[]) {
    return this.http.post(`${API_ENDPOINT}/entities/bulk-operation`, bulkRequestData).pipe(
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
    );
  }

  private getUrl(path: string, type: DomainType): string {
    return `${API_ENDPOINT}/${path !== ':' ? path : ''}/entitydomain/${type}`;
  }
}
